const { cpus } = require('node:os');

// 限制最多启动 2 个实例
const cpuLen = Math.min(cpus().length, 2);

module.exports = {
  apps: [
    {
      // 应用名称
      name: 'qct-app',
      // 启动脚本路径
      script: './dist/main.js',
      // 程序崩溃后自动重启
      autorestart: true,
      // 以集群模式运行
      exec_mode: 'cluster',
      // 关闭文件监视
      watch: false,
      // 启动实例数量,由 cpuLen 控制
      instances: cpuLen,
      // 内存超过 1G 时自动重启
      max_memory_restart: '3G',
      // 传递给脚本的参数
      args: '',
      // 环境变量配置
      env: {
        NODE_ENV: 'production',
      },
    },
  ],
};
