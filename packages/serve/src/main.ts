import multipart from '@fastify/multipart';
import { HttpStatus, Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { useContainer } from 'class-validator';
import { Configuration } from './config';

import { ApplicationModule } from './app.module';
import { DtoValidationException } from './common/exceptions/dto.exception';

// 提前加载
import './utils/smart-parse-address/load-address-info';

async function bootstrap() {
  const app = await NestFactory.create<NestFastifyApplication>(
    ApplicationModule,
    new FastifyAdapter({
      // 设置为 100MB
      bodyLimit: 100 * 1024 * 1024,
    }),
  );

  const configService = app.get(ConfigService);
  const { port, globalPrefix } = configService.get<Configuration['app']>('app');

  // class-validator 的 DTO 类中注入 nest 容器的依赖 (用于自定义验证器)
  useContainer(app.select(ApplicationModule), { fallbackOnErrors: true });

  // 注册 fastify 插件
  await app.register(multipart);

  app.setGlobalPrefix(globalPrefix);
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      transformOptions: { enableImplicitConversion: true },
      errorHttpStatusCode: HttpStatus.UNPROCESSABLE_ENTITY,
      stopAtFirstError: true,
      exceptionFactory: (errors) => {
        const error = errors[0];
        const rule = Object.keys(error.constraints!)[0];
        const msg = error.constraints![rule];

        return new DtoValidationException(msg);
      },
    }),
  );

  app.useStaticAssets({
    root: configService.get<Configuration['public']>('public'),
  });

  if (process.env.NODE_ENV === 'development') {
    app.enableCors();
  }

  const listenArgs = [port, process.env.LISTENING_ADDRESS].filter(Boolean);
  await app.listen(...listenArgs).then(async () => {
    new Logger('QCT').log(`应用启动成功，地址：http://127.0.0.1:${port}`);
  });
}

void bootstrap();
