import { getMetadataStorage } from 'class-validator';

// 用于组合继承多个类
// @see https://stackoverflow.com/questions/61779822/class-validator-decorators-on-typescript-mixin-classes
export function Mixins(derivedCtor: any, baseCtors: any[]) {
  const metadata = getMetadataStorage(); // from class-validator

  // Base typescript mixin implementation
  baseCtors.forEach((baseCtor) => {
    Object.getOwnPropertyNames(baseCtor.prototype).forEach((name) => {
      console.log(baseCtor.prototype);
      Object.defineProperty(
        derivedCtor.prototype,
        name,
        Object.getOwnPropertyDescriptor(baseCtor.prototype, name),
      );
    });
  });

  baseCtors.forEach((baseCtor) => {
    // 从 mixin 获取验证约束
    const constraints = metadata.getTargetValidationMetadatas(
      baseCtor.prototype.constructor,
      '',
      undefined,
      undefined,
    );

    for (const constraint of constraints) {
      // For each constraint on the mixin
      // Clone the constraint, replacing the target with the the derived constructor
      let clone = {
        ...constraint,
        target: derivedCtor.prototype.constructor,
      };
      // Set the prototype of the clone to be a validation metadata object
      clone = Object.setPrototypeOf(clone, Object.getPrototypeOf(constraint));
      console.log(clone);
      // Add the cloned constraint to class-validators metadata storage object
      metadata.addValidationMetadata(clone);
    }
  });
}
