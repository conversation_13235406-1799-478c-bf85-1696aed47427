import { customAlphabet, nanoid } from 'nanoid';

export function uuid(size: number = 21): string {
  return nanoid(size);
}

export function shortUUID(): string {
  return nanoid(10);
}

/**
 * 生成一个随机的值
 */
export function genRandomValue(
  length: number,
  placeholder = '1234567890qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM',
): string {
  const customNanoid = customAlphabet(placeholder, length);
  return customNanoid();
}

const RandomNumberValuePlaceholder = '1234567890';
export const genRandomNumberValue = (length: number) => {
  const customNanoid = customAlphabet(RandomNumberValuePlaceholder, length);
  return customNanoid();
};

export const generateRandomPassword = (len = 16) => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_!@#$%^&*()';
  let password = '';

  // 确保至少包含一个字母、数字、下划线和特殊字符
  password += characters.charAt(Math.floor(Math.random() * 52)); // 字母
  password += characters.charAt(52 + Math.floor(Math.random() * 10)); // 数字
  password += '_'; // 下划线
  password += characters.charAt(63 + Math.floor(Math.random() * 10)); // 特殊字符

  // 填充剩余的字符
  for (let i = 4; i < len; i += 1) {
    password += characters.charAt(
      Math.floor(Math.random() * characters.length),
    );
  }

  // 打乱密码字符顺序
  return password
    .split('')
    .sort(() => 0.5 - Math.random())
    .join('');
};

export const uniqueSlash = (path: string) =>
  path.replace(/(https?:\/)|(\/)+/g, '$1$2');

export const sleep = (ms: number) =>
  new Promise((resolve) => setTimeout(resolve, ms));
