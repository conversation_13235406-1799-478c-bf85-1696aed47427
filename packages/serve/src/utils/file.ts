import { MultipartFile } from '@fastify/multipart';
import { FileTypeValidator, MaxFileSizeValidator } from '@nestjs/common';
import { FileValidator } from '@nestjs/common/pipes/file/file-validator.interface';
import fs from 'node:fs';
import path from 'node:path';
import { genRandomValue } from '@/utils/tool';

enum Type {
  IMAGE = '图片',
  TXT = '文档',
  MUSIC = '音乐',
  VIDEO = '视频',
  OTHER = '其他',
}

export function getFileType(extName: string) {
  const documents = 'txt doc pdf ppt pps xlsx xls docx';
  const music = 'mp3 wav wma mpa ram ra aac aif m4a';
  const video = 'avi mpg mpe mpeg asf wmv mov qt rm mp4 flv m4v webm ogv ogg';
  const image =
    'bmp dib pcp dif wmf gif jpg tif eps psd cdr iff tga pcd mpt png jpeg';
  if (image.includes(extName)) return Type.IMAGE;

  if (documents.includes(extName)) return Type.TXT;

  if (music.includes(extName)) return Type.MUSIC;

  if (video.includes(extName)) return Type.VIDEO;

  return Type.OTHER;
}

export function getName(fileName: string) {
  if (fileName.includes('.')) return fileName.split('.')[0];

  return fileName;
}

export function getExtname(fileName: string) {
  return path.extname(fileName).replace('.', '');
}

export function getSize(bytes: number, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${Number.parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
}

export function fileRename(fileName: string) {
  const extName = path.extname(fileName);
  const name = fileName.replace(new RegExp(`${extName}$`), '');
  return `${name}_${genRandomValue(18)}${extName}`;
}

export function getFilePath(name: string, currentDate: string, type: string) {
  return `/upload/${currentDate}/${type}/${name}`;
}

export async function saveLocalFile(
  buffer: Buffer,
  name: string,
  currentDate: string,
  type: string,
) {
  const filePath = path.join(
    __dirname,
    '../../',
    'public/upload/',
    `${currentDate}/`,
    `${type}/`,
  );
  try {
    // 判断是否有该文件夹
    await fs.promises.stat(filePath);
  } catch (error) {
    // 没有该文件夹就创建
    await fs.promises.mkdir(filePath, { recursive: true });
  }
  const writeStream = fs.createWriteStream(filePath + name);
  writeStream.write(buffer);
}

export async function saveFile(file: MultipartFile, name: string) {
  const filePath = path.join(__dirname, '../../', 'public/upload', name);
  const writeStream = fs.createWriteStream(filePath);
  const buffer = await file.toBuffer();
  writeStream.write(buffer);
}

export async function deleteFile(name: string) {
  fs.unlink(path.join(__dirname, '../../', 'public', name), () => {
    // console.log(error);
  });
}

export const getFileFromPart = async (
  part: MultipartFile,
): Promise<IStorageMultipartFile> => {
  const buffer = await part.toBuffer();
  return {
    buffer,
    size: buffer.byteLength,
    filename: part.filename,
    mimetype: part.mimetype,
    fieldname: part.fieldname,
  };
};

export const validateFile = (
  file: IStorageMultipartFile,
  options: IStorageMultipartOptions,
): string | void => {
  const validators: FileValidator[] = [];

  if (options.maxFileSize)
    validators.push(
      new MaxFileSizeValidator({
        maxSize: options.maxFileSize,
        message: (maxSize) => `文件大小不能超过 ${getSize(maxSize)}`,
      }),
    );
  if (options.fileType)
    validators.push(
      new FileTypeValidator({
        fileType: options.fileType,
      }),
    );

  for (const validator of validators) {
    if (validator.isValid(file)) continue;

    return validator.buildErrorMessage(file);
  }
};
