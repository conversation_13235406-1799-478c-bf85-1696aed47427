import fs from 'fs';
import path from 'path';

/**
 * 地址数据处理
 * @param addressList-各级数据对象
 * @param index-对应的省/市/县区/街道
 * @param province-只有直辖市会处理为  北京市北京市
 * @returns <array>
 */
function formatAddressList(addressList, index, province) {
  if (index === 1) {
    //省
    addressList.province = addressList.name;
    addressList.type = 'province';
  }
  if (index === 2) {
    //市
    if (addressList.name == '市辖区') {
      addressList.name = province.name;
    }
    addressList.city = addressList.name;
    addressList.type = 'city';
  }
  if (index === 3) {
    //区或者县
    addressList.county = addressList.name;
    addressList.type = 'county';
  }
  if (index === 4) {
    //街道
    addressList.street = addressList.name;
    addressList.type = 'street';
  }
  if (addressList.children) {
    index++;
    addressList.children.forEach((res) => {
      formatAddressList(res, index, addressList);
    });
  }
}

export const AdministrativeAddressInformation = JSON.parse(
  fs.readFileSync(
    path.resolve(process.cwd(), 'public/static-address-area/1/full.json'),
    'utf8',
  ),
);

AdministrativeAddressInformation.forEach((item) => {
  formatAddressList(item, 1, '');
});
