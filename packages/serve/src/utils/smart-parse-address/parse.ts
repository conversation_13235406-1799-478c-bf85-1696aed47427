import { formatByKey } from './format';
import { AdministrativeAddressInformation } from './load-address-info';

// 定义地址解析结果的接口
interface AddressResult {
  province: string; // 省份名称
  provinceCode: string; // 省份编码
  city: string; // 城市名称
  cityCode: string; // 城市编码
  district: string; // 区县名称
  districtCode: string; // 区县编码
  street: string; // 街道名称
  streetCode: string; // 街道编码
  address: string; // 详细地址
  phone: string; // 联系电话
  name: string; // 收件人姓名
  idCard: string; // 身份证号
}

// 定义直辖市列表
const MUNICIPALITIES = ['北京市', '上海市', '天津市', '重庆市'];

// 定义身份证城市编码映射
const ID_CARD_CITY_MAP = {
  11: '北京',
  12: '天津',
  13: '河北',
  14: '山西',
  15: '内蒙古',
  21: '辽宁',
  22: '吉林',
  23: '黑龙江',
  31: '上海',
  32: '江苏',
  33: '浙江',
  34: '安徽',
  35: '福建',
  36: '江西',
  37: '山东',
  41: '河南',
  42: '湖北',
  43: '湖南',
  44: '广东',
  45: '广西',
  46: '海南',
  50: '重庆',
  51: '四川',
  52: '贵州',
  53: '云南',
  54: '西藏',
  61: '陕西',
  62: '甘肃',
  63: '青海',
  64: '宁夏',
  65: '新疆',
  71: '台湾',
  81: '香港',
  82: '澳门',
  91: '国外',
};

/**
 * 清理地址文本中的特殊字符
 */
function cleanupAddress(text: string): string {
  // 统一处理电话号码格式
  text = text.replace(/(\d{3})[-\s](\d{4})[-\s](\d{4})/g, '$1$2$3');

  // 移除特殊字符
  const pattern =
    /[`~!@$^&*()=|{}':;',\[\].<>/?~！@￥……&*（）——|{}【】'；：""'。，、？\r\n]/g;
  return text.replace(pattern, '');
}

/**
 * 提取并验证身份证号
 */
function extractIdCard(text: string): { isValid: boolean; value: string } {
  const idCardPattern = /^\d{17}(\d|X)$/i;
  const matches = text.match(idCardPattern);

  if (!matches) {
    return { isValid: false, value: '' };
  }

  const idCard = matches[0];
  const areaCode = idCard.substr(0, 2);

  // 验证地区编码
  if (!ID_CARD_CITY_MAP[areaCode]) {
    return { isValid: false, value: '' };
  }

  // 验证18位身份证校验位
  if (idCard.length === 18) {
    const factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    const parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
    const codes = idCard.split('');

    let sum = 0;
    for (let i = 0; i < 17; i++) {
      sum += parseInt(codes[i]) * factor[i];
    }

    if (parity[sum % 11].toString() !== codes[17].toUpperCase()) {
      return { isValid: false, value: '' };
    }
  }

  return { isValid: true, value: idCard };
}

/**
 * 提取电话号码
 */
function extractPhone(text: string): string | null {
  const phonePatterns = [
    /((\d{2,4}[-_－—])\d{3,8}([-_－—]?\d{3,8})?([-_－—]?\d{1,7})?)/,
    /(86-[1][0-9]{10})/,
    /(86[1][0-9]{10})/,
    /([1][0-9]{10})/,
  ];

  for (const pattern of phonePatterns) {
    const match = text.match(pattern);
    if (match && match[0].length >= 8) {
      return match[0];
    }
  }
  return null;
}

/**
 * 匹配省份信息
 */
function matchProvince(
  text: string,
): { province: string; code: string; matchValue: string } | null {
  let maxMatch = { province: '', code: '', matchValue: '', count: 0 };
  for (let i = 0; i < text.length; i++) {
    const slice = text.slice(0, i + 2);
    AdministrativeAddressInformation.forEach((province) => {
      if (province.province.indexOf(slice) !== -1) {
        if (slice.length > maxMatch.matchValue.length) {
          maxMatch = {
            province: province.province,
            code: province.code,
            matchValue: slice,
            count: 1,
          };
        }
      }
    });
  }

  return maxMatch.province ? maxMatch : null;
}

/**
 * 匹配城市信息
 */
function matchCity(
  text: string,
  provinceCode: string,
): Partial<AddressResult> | null {
  const result = {} as Partial<AddressResult>;
  let maxMatch = { matchValue: '', count: 0 };

  AdministrativeAddressInformation.forEach((province) => {
    if (province.code === provinceCode) {
      // 处理直辖市的特殊情况
      if (MUNICIPALITIES.includes(province.province)) {
        // 直辖市的区县直接作为城市处理
        province.children.forEach((district) => {
          district.children.forEach((county) => {
            if (text.indexOf(county.county) !== -1) {
              if (county.county.length > maxMatch.matchValue.length) {
                maxMatch = { matchValue: county.county, count: 1 };
                result.city = district.city;
                result.cityCode = district.code;
                result.district = county.county;
                result.districtCode = county.code;
              }
            }
          });
        });
      } else {
        // 非直辖市正常处理
        province.children.forEach((city) => {
          const cityName = city.city;
          if (text.indexOf(cityName) !== -1) {
            if (cityName.length > maxMatch.matchValue.length) {
              maxMatch = { matchValue: cityName, count: 1 };
              result.city = city.city;
              result.cityCode = city.code;
            }
          }
        });
      }
    }
  });

  return maxMatch.matchValue ? result : null;
}

/**
 * 匹配区县信息
 */
function matchCounty(
  text: string,
  cityCode: string,
): Partial<AddressResult> | null {
  const result = {} as Partial<AddressResult>;
  let maxMatch = { matchValue: '', count: 0 };

  AdministrativeAddressInformation.forEach((province) => {
    // 跳过直辖市的区县匹配，因为在matchCity中已经处理
    if (MUNICIPALITIES.includes(province.province)) {
      return;
    }

    province.children.forEach((city) => {
      if (city.code === cityCode) {
        city.children.forEach((county) => {
          if (text.indexOf(county.county) !== -1) {
            if (county.county.length > maxMatch.matchValue.length) {
              maxMatch = { matchValue: county.county, count: 1 };
              result.district = county.county;
              result.districtCode = county.code;
            }
          }
        });
      }
    });
  });

  return maxMatch.matchValue ? result : null;
}

/**
 * 匹配街道信息
 */
function matchStreet(
  text: string,
  countyCode: string,
): Partial<AddressResult> | null {
  const result = {} as Partial<AddressResult>;
  let maxMatch = { matchValue: '', count: 0 };

  AdministrativeAddressInformation.forEach((province) => {
    province.children.forEach((city) => {
      city.children.forEach((county) => {
        if (county.code === countyCode && county.children) {
          county.children.forEach((street) => {
            if (text.indexOf(street.street) !== -1) {
              if (street.street.length > maxMatch.matchValue.length) {
                maxMatch = { matchValue: street.street, count: 1 };
                result.street = street.street;
                result.streetCode = street.code;
              }
            }
          });
        }
      });
    });
  });

  return maxMatch.matchValue ? result : null;
}

/**
 * 解析地理位置信息
 */
function parseLocation(text: string): Partial<AddressResult> & {
  matchedTexts: string[];
} {
  const result = {} as Partial<AddressResult> & { matchedTexts: string[] };
  result.matchedTexts = [];

  // 解析省份
  const provinceInfo = matchProvince(text);
  if (provinceInfo) {
    result.province = provinceInfo.province;
    result.provinceCode = provinceInfo.code;
    result.matchedTexts.push(provinceInfo.matchValue);
    result.matchedTexts.push(provinceInfo.province);
  }

  // 解析城市
  if (result.provinceCode) {
    const cityInfo = matchCity(text, result.provinceCode);
    if (cityInfo) {
      Object.assign(result, cityInfo);
      if (cityInfo.city) {
        result.matchedTexts.push(cityInfo.city);
        // 添加带"市"的形式
        if (!cityInfo.city.endsWith('市')) {
          result.matchedTexts.push(cityInfo.city + '市');
        }
      }
    }
  }

  // 解析区县
  if (result.cityCode && !MUNICIPALITIES.includes(result.province)) {
    const countyInfo = matchCounty(text, result.cityCode);
    if (countyInfo) {
      Object.assign(result, countyInfo);
      if (countyInfo.district) {
        result.matchedTexts.push(countyInfo.district);
      }
    }
  }

  // 解析街道
  if (result.districtCode) {
    const streetInfo = matchStreet(text, result.districtCode);
    if (streetInfo) {
      Object.assign(result, streetInfo);
      if (streetInfo.street) {
        result.matchedTexts.push(streetInfo.street);
        if (!streetInfo.street.endsWith('街道')) {
          result.matchedTexts.push(streetInfo.street + '街道');
        }
      }
    }
  }

  return result;
}

/**
 * 智能解析地址文本
 */
export function parseAddress(addressText: string): AddressResult {
  const result = {} as AddressResult;
  let remainingText = cleanupAddress(addressText);

  // 提取身份证号
  const idCardInfo = extractIdCard(remainingText);
  if (idCardInfo.isValid) {
    result.idCard = idCardInfo.value;
    remainingText = remainingText.replace(idCardInfo.value, '');
  }

  // 提取电话号码
  const phoneInfo = extractPhone(remainingText);
  if (phoneInfo) {
    result.phone = phoneInfo;
    remainingText = remainingText.replace(phoneInfo, '');
  }

  // 存储所有需要从地址中清除的文本
  const textsToRemove = new Set<string>();

  // 解析地址组成部分
  const addressParts = remainingText.split(/\s+/).filter(Boolean);
  let maxLocationInfo: Partial<AddressResult> & { matchedTexts: string[] } = {
    matchedTexts: [],
  };

  // 找出包含最多地址信息的部分
  for (const part of addressParts) {
    if (part.length === 1) {
      result.name = part;
      textsToRemove.add(part);
      continue;
    }

    const locationInfo = parseLocation(part);
    if (
      Object.keys(locationInfo).length > maxLocationInfo.matchedTexts.length
    ) {
      maxLocationInfo = locationInfo;
    }
  }

  // 合并地址信息
  if (maxLocationInfo.matchedTexts.length > 0) {
    Object.assign(result, maxLocationInfo);
    maxLocationInfo.matchedTexts.forEach((text) => textsToRemove.add(text));
  }

  // 处理姓名
  addressParts.forEach((part) => {
    if (!result.name && part.length <= 4 && !textsToRemove.has(part)) {
      result.name = part;
      textsToRemove.add(part);
    }
  });

  // 处理详细地址
  if (result.province) {
    let detailAddress = remainingText;

    // 按长度降序排序，确保先移除较长的匹配文本
    const sortedTexts = Array.from(textsToRemove).sort(
      (a, b) => b.length - a.length,
    );

    // 移除所有匹配的文本
    sortedTexts.forEach((text) => {
      const regex = new RegExp(
        text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
        'g',
      );
      detailAddress = detailAddress.replace(regex, '');
    });

    // 清理地址文本
    result.address = detailAddress
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/,|，/g, '')
      .replace(/^[、 ]+|[、 ]+$/g, '')
      .replace(/^[省市区县街道]+|[省市区县街道]+$/g, '');
  }

  return result;
}
