import { defineConfig } from '@mikro-orm/postgresql';
import { SqlHighlighter } from '@mikro-orm/sql-highlighter';
import { TsMorphMetadataProvider } from '@mikro-orm/reflection';

export default defineConfig({
  host: 'localhost',
  port: 5432,
  user: 'zhichengliang',
  password: '',
  dbName: 'stardust',
  entities: ['dist/**/*.entity.js'],
  entitiesTs: ['src/**/*.entity.ts'],
  debug: true,
  migrations: {
    path: 'src/database/migrations',
  },
  metadataCache: {
    options: {
      cacheDir: 'node_modules/.cache/mikro-orm',
    },
  },
  highlighter: new SqlHighlighter(),
  metadataProvider: TsMorphMetadataProvider,
});
