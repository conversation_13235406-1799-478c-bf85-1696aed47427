import { envNumber } from '@/utils';
import { PostgreSqlDriver } from '@mikro-orm/postgresql';
import path from 'path';

const basePath = path.join(__dirname, '..');

export const configuration = () => {
  return {
    // 是否为开发环境
    dev: process.env.NODE_ENV === 'development',
    basePath,

    app: {
      // 应用名称
      name: process.env.APP_NAME || '全程通物流平台',
      // 全局API前缀
      globalPrefix: process.env.GLOBAL_PREFIX || 'api',
      // 应用端口
      port: envNumber(process.env.PORT, 7813),
    },

    database: {
      // 数据库主机
      host: process.env.DATABASE_HOST || 'localhost',
      // 数据库端口
      port: envNumber(process.env.DATABASE_PORT, 5432),
      // 数据库用户名
      user: process.env.DATABASE_USER || 'zhichengliang',
      // 数据库密码
      password: process.env.DATABASE_PASSWORD || '',
      // 数据库名称
      dbName: process.env.DATABASE_NAME || 'quanchengtong',
      // 实体文件路径（编译后）
      entities: ['dist/modules/**/*.entity.js'],
      // 实体文件路径（TypeScript源文件）
      entitiesTs: ['src/modules/**/*.entity.ts'],
      // 数据库驱动
      driver: PostgreSqlDriver,
    },

    redis: {
      // 数据库主机
      host: process.env.REDIS_HOST || '127.0.0.1',
      // 数据库端口
      port: envNumber(process.env.REDIS_PORT, 6379),
      // 数据库密码
      password: process.env.REDIS_PASSWORD,
      // 数据库名称
      db: envNumber(process.env.REDIS_DB, 0),
    },

    logger: {
      // 日志级别
      level: process.env.LOGGER_LEVEL || 'info',
      // 最大日志文件数
      maxFiles: envNumber(process.env.LOGGER_MAX_FILES, 10),
    },

    security: {
      jwt: {
        // JWT密钥
        secret: process.env.JWT_SECRET || 'quan_cheng_tong',
        // JWT过期时间，默认 7 天，单位：秒
        expire: envNumber(process.env.JWT_EXPIRE, 7 * 24 * 60 * 60),
        // JWT刷新密钥
        refreshSecret: process.env.JWT_REFRESH_SECRET,
        // JWT刷新过期时间，默认 2 周，单位：秒
        refreshExpire: envNumber(
          process.env.JWT_REFRESH_EXPIRE,
          2 * 7 * 24 * 60 * 60,
        ),
      },
    },

    mailer: {
      // SMTP 服务器主机
      host: process.env.SMTP_HOST,
      // SMTP 服务器端口
      port: envNumber(process.env.SMTP_PORT),
      // 忽略TLS
      ignoreTLS: true,
      // 使用安全连接
      secure: true,
      // 认证信息
      auth: {
        // SMTP 用户名
        user: process.env.SMTP_USER,
        // SMTP 密码
        pass: process.env.SMTP_PASS,
      },
    },

    captcha: {
      image: {
        imageBasePath: path.resolve(basePath, 'assets/images'),
        pc: {
          width: 320,
          height: 160,
        },
        mobile: {
          width: 100,
          height: 50,
        },
        // 5 分钟 过期时间，单位：秒
        expire: 60 * 5,
      },
    },

    pagination: {
      // 默认分页大小
      pageSize: 20,
      // 最大分页大小
      maxPageSize: 9999,
    },

    public: path.join(__dirname, '..', '..', 'public'),
  };
};

// 配置类型定义
export type Configuration = ReturnType<typeof configuration>;
