import { MikroOrmModule } from '@mikro-orm/nestjs';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, RouterModule } from '@nestjs/core';
import { ThrottlerModule } from '@nestjs/throttler';

import { AllExceptionsFilter } from './common/filters/all-exception.filter';
import { Configuration, configuration } from './config';
import { AppModule } from './modules/app/app.module';
import { AuthModule } from './modules/auth/auth.module';
import { JwtAuthGuard } from './modules/auth/guards/jwt-auth.guard';
import { BaseModule } from './modules/base/base.module';
import { FinanceModule } from './modules/finance/finance.module';
import { FreightModule } from './modules/freight/freight.module';
// import { HeadlessModule } from './modules/headless/headless.module';
import { LogisticsModule } from './modules/logistics/logistics.module';
import { SharedModule } from './modules/shared/shared.module';
import { SSEModule } from './modules/sse/sse.module';
import { StatisticsModule } from './modules/statistics/statistics.module';
import { SystemModule } from './modules/system/system.module';
import { UserModule } from './modules/user/user.module';

@Module({
  imports: [
    ThrottlerModule.forRoot([
      {
        ttl: 60 * 1000,
        limit: 66,
      },
    ]),
    ConfigModule.forRoot({
      cache: true,
      isGlobal: true,
      load: [configuration],
      envFilePath: ['.env.local', '.env'],
    }),
    MikroOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService<Configuration>) => {
        const database =
          configService.get<Configuration['database']>('database');

        return {
          ...database,
          port: process.env.DATABASE_PORT
            ? parseInt(process.env.DATABASE_PORT)
            : database.port,
          // debug: configService.get('dev'),
        };
      },
    }),
    SharedModule,
    BaseModule,
    AuthModule,
    UserModule,
    SystemModule,
    LogisticsModule,
    FreightModule,
    AppModule,
    StatisticsModule,
    // HeadlessModule,
    SSEModule,
    FinanceModule,
    RouterModule.register([
      {
        path: 'app',
        module: AppModule,
      },
    ]),
  ],
  providers: [
    { provide: APP_FILTER, useClass: AllExceptionsFilter },

    // { provide: APP_INTERCEPTOR, useClass: ClassSerializerInterceptor },
    // { provide: APP_INTERCEPTOR, useClass: TransformInterceptor },
    // {
    //   provide: APP_INTERCEPTOR,
    //   useFactory: () => new TimeoutInterceptor(15 * 1000),
    // },
    // { provide: APP_INTERCEPTOR, useClass: IdempotenceInterceptor },

    { provide: APP_GUARD, useClass: JwtAuthGuard },

    // { provide: APP_GUARD, useClass: RbacGuard },
    // { provide: APP_GUARD, useClass: ThrottlerGuard },
  ],
})
export class ApplicationModule {}
