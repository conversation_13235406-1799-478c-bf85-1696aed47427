{"name": "@stardust/root", "private": true, "workspaces": ["packages/*"], "scripts": {"admin:dev": "pnpm run -C packages/admin dev", "serve:dev": "pnpm run -C packages/serve dev", "build": "bun run clean:dist && pnpm run -r build", "clean": "pnpm run clean:dist && pnpm run -r --parallel clean", "clean:dist": "rimraf packages/*/dist", "play": "pnpm run -C packages/playground dev", "lint": "prettier -c --parser typescript \"packages/*/{src,__tests__,e2e}/**/*.[jt]s?(x)\"", "lint:fix": "pnpm run lint --write", "test": "pnpm run -r dev:prepare && pnpm run test:types && pnpm run test:vitest && pnpm run -r test && pnpm run build && pnpm run build:dts && pnpm test:dts", "test:vitest": "vitest run --coverage", "test:types": "tsc --build ./tsconfig.json", "up": "taze major -I", "ri": "rimraf -rf packages/*/node_modules && rimraf -rf node_modules && pnpm i"}, "dependencies": {}, "devDependencies": {"lint-staged": "^16.1.2", "rimraf": "^6.0.1", "standard-version": "^9.5.0", "taze": "^19.1.0", "typescript": "^5.8.3"}}