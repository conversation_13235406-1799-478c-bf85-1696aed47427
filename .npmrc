
registry=https://registry.npmmirror.com

disturl=https://npmmirror.com/dist

shamefully-hoist=true
strict-peer-dependencies=false

# https://github.com/cnpm/binary-mirror-config/blob/main/package.json#L48

# node-gyp
NODEJS_ORG_MIRROR=https://npm.npmmirror.com/mirrors/node
IOJS_ORG_MIRROR=https://npmmirror.com/mirrors/iojs

SWC_BINARY_SITE=https://npmmirror.com/mirrors/node-swc

# Electron
electron_mirror=https://npmmirror.com/mirrors/electron/
ELECTRON_BUILDER_BINARIES_MIRROR=http://npmmirror.com/mirrors/electron-builder-binaries/

# Browser drivers
phantomjs_cdnurl=https://npmmirror.com/mirrors/phantomjs
chromedriver_cdnurl=https://npmmirror.com/mirrors/chromedriver
operadriver_cdnurl=https://npmmirror.com/mirrors/operadriver

# canvas
canvas_binary_host_mirror=https://npmmirror.com/mirrors/canvas/
npm_config_canvas_binary_host_mirror=https://npmmirror.com/mirrors/node-canvas-prebuilt

# sharp
sharp_dist_base_url=https://npmmirror.com/mirrors/sharp-libvips/
npm_config_sharp_binary_host=https://npmmirror.com/mirrors/sharp/

puppeteer_download_host=https://npmmirror.com/mirrors/

python_mirror=https://npmmirror.com/mirrors/python/

fse_binary_host_mirror=https://npmmirror.com/mirrors/fsevents

sass_binary_site=http://npmmirror.com/mirrors/node-sass

SENTRYCLI_CDNURL=https://npmmirror.com/mirrors/sentry-cli
